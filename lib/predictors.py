"""
预测器模块

包含各种权重重要性预测算法：
1. 先进融合预测器（最新技术）
2. 增强预测器（默认）
3. Wanda预测器
4. SNIP预测器
"""

import torch
import torch.nn.functional as F
import math
from typing import Dict, Optional, Tuple


class PredictorRegistry:
    """预测器注册表，管理所有可用的预测器"""

    @staticmethod
    def advanced_fusion_predictor(activation_stats, weight_matrix, layer_info):
        """
        先进融合预测器 - 融合最新研究成果的高级重要性预测算法

        核心技术融合：
        1. 二阶信息（Fisher Information & Hessian近似）
        2. 激活感知剪枝（Activation-aware）
        3. 梯度感知剪枝（Gradient-aware）
        4. 多尺度重要性评估
        5. 自适应权重融合
        6. 层级感知优化

        基于最新论文：
        - SparseGPT, WANDA, OBS等经典方法
        - 2024年最新的二阶优化技术
        - 激活统计的高级利用方法

        Args:
            activation_stats: 激活统计信息字典
            weight_matrix: 当前层的权重矩阵 [out_features, in_features]
            layer_info: 层信息（layer_id, layer_name等）

        Returns:
            importance_scores: 每个权重的重要性分数 [out_features, in_features]
        """
        # 获取基础统计信息
        l2_norm = activation_stats['l2_norm']      # [in_features]
        variance = activation_stats['var']         # [in_features]
        mean = activation_stats['mean']            # [in_features]
        mean_abs = torch.abs(mean)

        weight_abs = torch.abs(weight_matrix)      # [out_features, in_features]
        weight_squared = weight_matrix ** 2        # [out_features, in_features]

        # === 1. 二阶信息近似（Fisher Information & Hessian） ===
        # Fisher信息矩阵的对角近似
        fisher_diag = variance + 1e-8  # 使用激活方差作为Fisher信息的近似

        # Hessian对角近似（基于激活的二阶矩）
        second_moment = l2_norm + mean**2  # E[x^2] = Var[x] + E[x]^2
        hessian_diag = second_moment + 1e-8

        # 二阶重要性分数：|w|^2 * H_ii（OBS风格）
        second_order_importance = weight_squared * hessian_diag.unsqueeze(0)

        # === 2. 激活感知重要性（WANDA风格增强） ===
        # 多尺度激活分析
        l2_scale = torch.sqrt(l2_norm + 1e-8)
        var_scale = torch.sqrt(variance + 1e-8)
        mean_scale = torch.sqrt(mean_abs + 1e-8)

        # 激活强度的综合评估
        activation_intensity = (0.5 * l2_scale + 0.3 * var_scale + 0.2 * mean_scale)

        # 激活感知重要性：|w| * sqrt(activation_stats)
        activation_aware_importance = weight_abs * activation_intensity.unsqueeze(0)

        # === 3. 梯度感知重要性（SNIP风格增强） ===
        # 梯度近似：使用激活统计来近似梯度信息
        grad_proxy = torch.sqrt(fisher_diag)  # 梯度幅度的近似

        # 梯度感知重要性：|w| * |∇w|
        gradient_aware_importance = weight_abs * grad_proxy.unsqueeze(0)

        # === 4. 多尺度重要性评估 ===
        # 局部重要性：每个神经元内的相对重要性
        neuron_weight_mean = torch.mean(weight_abs, dim=1, keepdim=True)
        neuron_weight_std = torch.std(weight_abs, dim=1, keepdim=True)
        local_importance = (weight_abs - neuron_weight_mean) / (neuron_weight_std + 1e-8)

        # 全局重要性：整个层的相对重要性
        global_weight_mean = torch.mean(weight_abs)
        global_weight_std = torch.std(weight_abs)
        global_importance = (weight_abs - global_weight_mean) / (global_weight_std + 1e-8)

        # === 5. 自适应权重融合 ===
        # 根据激活分布特性动态调整各分量权重
        l2_strength = torch.mean(l2_norm).item()
        var_strength = torch.mean(variance).item()
        mean_strength = torch.mean(mean_abs).item()

        # 归一化强度
        total_strength = l2_strength + var_strength + mean_strength + 1e-8
        l2_ratio = l2_strength / total_strength
        var_ratio = var_strength / total_strength
        mean_ratio = mean_strength / total_strength

        # 自适应权重计算
        w_second_order = 0.3 + 0.2 * var_ratio  # 方差高时更依赖二阶信息
        w_activation = 0.4 + 0.2 * l2_ratio     # L2强度高时更依赖激活感知
        w_gradient = 0.2 + 0.1 * mean_ratio     # 均值大时更依赖梯度信息
        w_local = 0.05
        w_global = 0.05

        # 归一化权重
        total_w = w_second_order + w_activation + w_gradient + w_local + w_global
        w_second_order /= total_w
        w_activation /= total_w
        w_gradient /= total_w
        w_local /= total_w
        w_global /= total_w

        # === 6. 融合重要性分数 ===
        fused_importance = (
            w_second_order * second_order_importance +
            w_activation * activation_aware_importance +
            w_gradient * gradient_aware_importance +
            w_local * torch.abs(local_importance) +
            w_global * torch.abs(global_importance)
        )

        # === 7. 层级感知优化 ===
        if layer_info:
            layer_name = layer_info.get('layer_name', '').lower()
            layer_id = layer_info.get('layer_id', 0)

            # 层类型特定优化
            if 'q_proj' in layer_name:
                # Query投影：注意力核心，偏向二阶信息
                type_factor = 1.15
                second_order_boost = 1.2
            elif 'k_proj' in layer_name:
                # Key投影：注意力核心，偏向激活感知
                type_factor = 1.12
                second_order_boost = 1.1
            elif 'v_proj' in layer_name:
                # Value投影：信息载体，平衡各方法
                type_factor = 1.08
                second_order_boost = 1.0
            elif 'o_proj' in layer_name:
                # Output投影：整合输出，偏向梯度信息
                type_factor = 1.05
                second_order_boost = 0.9
            elif 'gate_proj' in layer_name:
                # Gate投影：门控机制，偏向激活感知
                type_factor = 1.10
                second_order_boost = 1.0
            elif 'up_proj' in layer_name:
                # Up投影：特征扩展，标准处理
                type_factor = 1.00
                second_order_boost = 1.0
            elif 'down_proj' in layer_name:
                # Down投影：特征压缩，可以更激进
                type_factor = 0.92
                second_order_boost = 0.8
            else:
                type_factor = 1.0
                second_order_boost = 1.0

            # 深度特定优化
            if layer_id < 4:
                # 早期层：更保守，偏向二阶信息
                depth_factor = 1.08
                second_order_boost *= 1.1
            elif layer_id < 12:
                # 中前期层：轻微保守
                depth_factor = 1.04
                second_order_boost *= 1.05
            elif layer_id > 20:
                # 后期层：可以更激进
                depth_factor = 0.96
                second_order_boost *= 0.95
            else:
                # 中间层：标准处理
                depth_factor = 1.0
                second_order_boost *= 1.0

            # 应用层级优化
            final_importance = fused_importance * type_factor * depth_factor

            # 对二阶分量进行额外调整
            if second_order_boost != 1.0:
                second_order_component = w_second_order * second_order_importance * second_order_boost
                other_components = (
                    w_activation * activation_aware_importance +
                    w_gradient * gradient_aware_importance +
                    w_local * torch.abs(local_importance) +
                    w_global * torch.abs(global_importance)
                )
                final_importance = (second_order_component + other_components) * type_factor * depth_factor
        else:
            final_importance = fused_importance

        # === 8. 数值稳定性和后处理 ===
        # 确保数值稳定性
        final_importance = torch.clamp(final_importance, min=1e-8)

        # 可选：应用非线性变换增强区分度
        # final_importance = torch.pow(final_importance, 0.8)  # 轻微压缩动态范围

        return final_importance

    @staticmethod
    def dolphin_predictor(activation_stats, weight_matrix, layer_info):
        """
        优化的自适应预测器 - 专门针对高稀疏度和复杂场景设计

        核心特点：
        1. 结合多种激活统计信息进行更精细的重要性评估
        2. 对高稀疏度场景进行特殊优化
        3. 使用自适应权重分配策略
        4. 增强对异常值的鲁棒性

        Args:
            activation_stats: 激活统计信息字典
            weight_matrix: 当前层的权重矩阵 [out_features, in_features]
            layer_info: 层信息（layer_id, layer_name等）

        Returns:
            importance_scores: 每个权重的重要性分数 [out_features, in_features]
        """
        # 获取激活统计
        l2_norm = activation_stats['l2_norm']      # [in_features]
        variance = activation_stats['var']         # [in_features]
        mean_abs = torch.abs(activation_stats['mean'])  # [in_features]

        # === 多尺度激活分析 ===
        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]

        # 1. 基础WANDA分量
        wanda_scale = torch.sqrt(l2_norm + 1e-8)

        # 2. 方差敏感性分量 - 高方差表示更重要的特征
        variance_normalized = variance / (torch.mean(variance) + 1e-8)
        variance_scale = torch.sqrt(variance_normalized + 1e-8)

        # 3. 均值稳定性分量 - 考虑激活的稳定性
        mean_scale = torch.sqrt(mean_abs / (torch.mean(mean_abs) + 1e-8) + 1e-8)

        # === 自适应权重组合 ===
        # 根据激活分布特性动态调整各分量权重
        l2_strength = torch.mean(l2_norm).item()
        var_strength = torch.mean(variance).item()

        # 动态权重：L2强度高时更依赖WANDA，方差大时更依赖方差分量
        alpha = 0.6 + 0.2 * min(l2_strength / (l2_strength + var_strength + 1e-8), 1.0)
        beta = 0.3 * (1.0 - alpha + 0.6)
        gamma = 1.0 - alpha - beta

        # 组合激活尺度
        activation_scale = (alpha * wanda_scale +
                           beta * variance_scale +
                           gamma * mean_scale).unsqueeze(0)

        # 基础重要性分数
        base_importance = weight_abs * activation_scale

        # === 权重分布分析 ===
        # 分析每个输出神经元的权重分布特性
        weight_std = torch.std(weight_abs, dim=1, keepdim=True)  # [out_features, 1]
        weight_mean = torch.mean(weight_abs, dim=1, keepdim=True)  # [out_features, 1]

        # 权重变异系数：衡量权重分布的不均匀性
        weight_cv = weight_std / (weight_mean + 1e-8)

        # 相对重要性：权重相对于该神经元平均权重的重要性
        relative_importance = weight_abs / (weight_mean + 1e-8)

        # === 层感知调整 ===
        layer_name = layer_info.get('layer_name', '').lower()
        layer_id = layer_info.get('layer_id', 0)

        # 层类型权重
        if 'q_proj' in layer_name or 'k_proj' in layer_name:
            layer_factor = 1.15  # 注意力核心，更保守
        elif 'v_proj' in layer_name:
            layer_factor = 1.10  # 值投影，稍保守
        elif 'o_proj' in layer_name:
            layer_factor = 1.05  # 输出投影，轻微保守
        elif 'gate_proj' in layer_name:
            layer_factor = 1.08  # 门控，保守
        elif 'up_proj' in layer_name:
            layer_factor = 1.00  # 上投影，标准
        elif 'down_proj' in layer_name:
            layer_factor = 0.92  # 下投影，可以更激进
        else:
            layer_factor = 1.0

        # 深度调整
        if layer_id < 4:
            depth_factor = 1.08
        elif layer_id > 20:
            depth_factor = 0.95
        else:
            depth_factor = 1.0

        # === 最终重要性计算 ===
        # 结合基础重要性、相对重要性和权重分布特性
        final_importance = (0.7 * base_importance +
                           0.2 * relative_importance +
                           0.1 * weight_cv.expand_as(weight_abs))

        # 应用层感知调整
        final_importance = final_importance * layer_factor * depth_factor

        return final_importance

    @staticmethod
    def wanda_predictor(activation_stats, weight_matrix, layer_info):
        """
        优化的WANDA预测器 - 保持简洁性的同时提升稳定性和效果

        核心优化：
        1. 保持WANDA的核心公式：|权重| × sqrt(激活统计)
        2. 改进激活统计的计算方式，提升数值稳定性
        3. 加入轻量级的自适应调整
        4. 优化层感知策略

        Args:
            activation_stats: 激活统计信息字典
            weight_matrix: 当前层的权重矩阵 [out_features, in_features]
            layer_info: 层信息（layer_id, layer_name等）

        Returns:
            importance_scores: 每个权重的重要性分数 [out_features, in_features]
        """
        # 获取激活统计
        l2_norm = activation_stats['l2_norm']
        variance = activation_stats['var']
        weight_abs = torch.abs(weight_matrix)

        # === 改进的激活尺度计算 ===
        # 1. 基础L2范数尺度（WANDA核心）
        l2_scale = torch.sqrt(l2_norm + 1e-8)

        # 2. 方差稳定性调整
        # 使用方差来调节激活的稳定性，避免过度依赖异常激活
        var_normalized = variance / (torch.mean(variance) + 1e-8)
        stability_factor = torch.clamp(var_normalized, 0.5, 2.0)  # 限制在合理范围内

        # 3. 组合激活尺度
        # 主要依赖L2范数，稳定性因子作为调节
        activation_scale = (l2_scale * torch.sqrt(stability_factor)).unsqueeze(0)

        # === 基础重要性计算 ===
        base_importance = weight_abs * activation_scale

        # === 自适应层感知调整 ===
        if layer_info:
            layer_name = layer_info.get('layer_name', '').lower()
            layer_id = layer_info.get('layer_id', 0)

            # 层类型调整 - 基于Transformer架构的先验知识
            if 'q_proj' in layer_name:
                layer_factor = 1.08  # Query投影：注意力核心，保守
            elif 'k_proj' in layer_name:
                layer_factor = 1.06  # Key投影：注意力核心，稍保守
            elif 'v_proj' in layer_name:
                layer_factor = 1.04  # Value投影：信息载体，轻微保守
            elif 'o_proj' in layer_name:
                layer_factor = 1.02  # Output投影：整合输出，轻微保守
            elif 'gate_proj' in layer_name:
                layer_factor = 1.05  # Gate投影：FFN门控，保守
            elif 'up_proj' in layer_name:
                layer_factor = 1.00  # Up投影：FFN扩展，标准
            elif 'down_proj' in layer_name:
                layer_factor = 0.96  # Down投影：FFN压缩，可以更激进
            else:
                layer_factor = 1.0

            # 深度调整 - 早期层更重要
            if layer_id < 4:
                depth_factor = 1.04  # 早期层保守
            elif layer_id < 12:
                depth_factor = 1.02  # 中前期层轻微保守
            elif layer_id > 20:
                depth_factor = 0.98  # 后期层可以更激进
            else:
                depth_factor = 1.0   # 中间层标准

            # 应用调整因子
            importance_scores = base_importance * layer_factor * depth_factor
        else:
            importance_scores = base_importance

        return importance_scores
    

    @staticmethod
    def snip_predictor(activation_stats, weight_matrix, layer_info=None):
        """
        优化的SNIP预测器 - 基于梯度敏感性的改进版本

        核心改进：
        1. 更精确的梯度近似方法
        2. 结合多种激活统计信息
        3. 针对不同层类型的特殊优化
        4. 增强数值稳定性

        Args:
            activation_stats: 激活统计信息字典
            weight_matrix: 当前层的权重矩阵 [out_features, in_features]
            layer_info: 层信息（可选）

        Returns:
            importance_scores: 权重重要性分数
        """
        # 获取激活统计
        variance = activation_stats['var']
        l2_norm = activation_stats['l2_norm']
        mean_abs = torch.abs(activation_stats['mean'])
        weight_abs = torch.abs(weight_matrix)

        # === 改进的梯度近似 ===
        # 1. 基础方差分量（原始SNIP思想）
        var_component = variance

        # 2. L2范数分量（激活强度）
        l2_component = l2_norm

        # 3. 均值分量（激活偏置）
        mean_component = mean_abs

        # === 自适应权重组合 ===
        # 根据激活分布特性动态调整各分量权重
        var_strength = torch.mean(variance).item()
        l2_strength = torch.mean(l2_norm).item()
        mean_strength = torch.mean(mean_abs).item()

        total_strength = var_strength + l2_strength + mean_strength + 1e-8

        # 动态权重：强度大的分量获得更高权重
        w_var = 0.5 + 0.3 * (var_strength / total_strength)
        w_l2 = 0.3 + 0.2 * (l2_strength / total_strength)
        w_mean = 0.2 + 0.1 * (mean_strength / total_strength)

        # 归一化权重
        total_w = w_var + w_l2 + w_mean
        w_var, w_l2, w_mean = w_var/total_w, w_l2/total_w, w_mean/total_w

        # 组合梯度近似
        grad_approx = (w_var * var_component +
                      w_l2 * l2_component +
                      w_mean * mean_component)

        # 数值稳定性处理
        grad_approx = torch.clamp(grad_approx, min=1e-8)
        grad_scale = torch.sqrt(grad_approx).unsqueeze(0)

        # === 基础重要性计算 ===
        base_importance = weight_abs * grad_scale

        # === 层感知调整（如果提供层信息）===
        if layer_info:
            layer_name = layer_info.get('layer_name', '').lower()
            layer_id = layer_info.get('layer_id', 0)

            # SNIP对不同层类型的适应性调整
            if 'q_proj' in layer_name or 'k_proj' in layer_name:
                # 注意力层：梯度信息更重要
                layer_factor = 1.10
            elif 'v_proj' in layer_name:
                # 值投影：中等重要性
                layer_factor = 1.05
            elif 'o_proj' in layer_name:
                # 输出投影：梯度敏感性中等
                layer_factor = 1.02
            elif 'gate_proj' in layer_name:
                # 门控：梯度信息重要
                layer_factor = 1.08
            elif 'up_proj' in layer_name or 'down_proj' in layer_name:
                # FFN投影：SNIP效果较好
                layer_factor = 1.00
            else:
                layer_factor = 1.0

            # 深度调整：SNIP在中后期层效果更好
            if layer_id < 6:
                depth_factor = 0.95  # 早期层效果一般
            elif layer_id > 15:
                depth_factor = 1.05  # 后期层效果更好
            else:
                depth_factor = 1.0   # 中间层标准

            importance_scores = base_importance * layer_factor * depth_factor
        else:
            importance_scores = base_importance

        return importance_scores

    @classmethod
    def get_predictor(cls, strategy="advanced"):
        """
        获取指定策略的预测器

        Args:
            strategy: 预测器策略名称
                - "advanced": 先进融合预测器（最新技术，推荐，默认）
                - "dolphin": 优化的Giraffe预测器
                - "wanda": 增强WANDA预测器
                - "snip": SNIP预测器

        Returns:
            predictor: 预测器函数
        """
        strategy_map = {
            "advanced": cls.advanced_fusion_predictor,
            "dolphin": cls.dolphin_predictor,
            "wanda": cls.wanda_predictor,
            "snip": cls.snip_predictor
        }

        if strategy in strategy_map:
            return strategy_map[strategy]
        else:
            available = list(strategy_map.keys())
            raise ValueError(f"未知策略 '{strategy}'，可用策略: {available}")

    @classmethod
    def list_available_strategies(cls):
        """获取所有可用的预测器策略"""
        return ["advanced", "dolphin", "wanda", "snip"]
