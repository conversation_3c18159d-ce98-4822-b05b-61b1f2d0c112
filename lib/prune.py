import time
import numpy as np
import torch
import torch.nn as nn
from CKA import cka  # 导入CKA（Centered Kernel Alignment）模块，用于计算层间特征相似性
from scipy import optimize  # 用于优化算法，计算每层的最优稀疏度分配
import math
import gc

from .sparsegpt import SparseGPT  # 导入SparseGPT剪枝算法
from .layerwrapper import WrappedGPT  # 导入层包装器，用于收集激活统计信息
from .giraffe import Giraffe

def allocate_ranks(loss_values, rank):
    """
    动态分配LoRA秩的核心函数
    
    基本原理：
    1. 根据每层的重构损失（reconstruction loss）来分配LoRA的秩
    2. 损失越大的层，分配的秩越高，以获得更强的恢复能力
    3. 确保总的平均秩保持在指定范围内
    
    Args:
        loss_values: 每层的重构损失数组
        rank: 目标平均秩
        
    Returns:
        adjusted_ranks: 为每层分配的LoRA秩列表
    """
    # 设置LoRA秩的范围约束
    min_rank = 1    # 最小秩：保证每层至少有基本的适应能力
    max_rank = 16   # 最大秩：防止过拟合和参数爆炸
    
    # 计算总损失，用于归一化
    total_loss = np.sum(loss_values)
    
    # 基于损失比例进行初始秩分配
    # 公式：每层初始秩 = (该层损失/总损失) * 目标平均秩 * 层数
    initial_ranks = (loss_values / total_loss) * rank * len(loss_values)
    
    # 将初始秩四舍五入并约束在[min_rank, max_rank]范围内
    adjusted_ranks = np.clip(np.round(initial_ranks), min_rank, max_rank).astype(int)
    # 计算调整后的总秩与目标总秩的差异
    total_adjusted_rank = np.sum(adjusted_ranks)
    diff = int(total_adjusted_rank - rank*len(loss_values))
    
    # 如果总秩超出目标，需要减少某些层的秩
    if diff > 0:
        # 找出所有秩大于最小值的层
        indices = np.where(adjusted_ranks > min_rank)[0]
        # 按损失从小到大排序，优先减少损失较小层的秩
        sorted_indices = indices[np.argsort(loss_values[indices])]
        idx = 0
        while diff > 0 and idx < len(sorted_indices):
            adj_idx = sorted_indices[idx]
            if adjusted_ranks[adj_idx] > min_rank:
                adjusted_ranks[adj_idx] -= 1  # 减少1个秩
                diff -=1
            idx +=1
    # 如果总秩不足目标，需要增加某些层的秩        
    elif diff < 0:
        diff = -diff
        # 找出所有秩小于最大值的层
        indices = np.where(adjusted_ranks < max_rank)[0]
        # 按损失从大到小排序，优先增加损失较大层的秩
        sorted_indices = indices[np.argsort(-loss_values[indices])]
        idx = 0
        while diff > 0 and idx < len(sorted_indices):
            adj_idx = sorted_indices[idx]
            if adjusted_ranks[adj_idx] < max_rank:
                adjusted_ranks[adj_idx] += 1  # 增加1个秩
                diff -=1
            idx +=1
    
    # 返回调整后的秩数组
    return adjusted_ranks


def get_feature_map(args, model, tokenizer, device=torch.device("cuda:0"), dataloader=None):
    """
    获取模型各层的特征图（feature map）
    
    核心作用：
    1. 提取模型在密集状态下各层的输出特征
    2. 这些特征将作为剪枝后重构损失计算的参考基准
    3. 通过比较剪枝前后的特征差异来评估剪枝质量
    
    Args:
        args: 命令行参数
        model: 预训练模型
        tokenizer: 分词器  
        device: 计算设备
        dataloader: 校准数据加载器
        
    Returns:
        feature: 包含各层特征图的列表
    """
    model.config.use_cache = False
    
    # 准备校准输入数据
    with torch.no_grad():
        print("Preparing calibration input...")
        inps, outs, attention_mask, position_ids = prepare_calibration_input(
            args, model, dataloader, device
        )
        print("inps shape:", inps.shape)
        print("outs shape:", outs.shape)
        print("attention_mask:", attention_mask)
        print("position_ids:", position_ids)
    
    layers = model.model.layers

    feature = []  # 存储各层特征图的列表
    
    # 逐层提取特征
    for i in range(len(layers)):
        layer = layers[i]

        # 找到当前层中的所有线性层（不包括LoRA层）
        subset = find_layers(layer)

        # 处理多GPU情况下的设备映射
        if f"model.layers.{i}" in model.hf_device_map:   
            dev = model.hf_device_map[f"model.layers.{i}"]
            inps, outs, attention_mask, position_ids = inps.to(dev), outs.to(dev), attention_mask.to(dev), position_ids.to(dev)

        # 为每个线性层创建包装器来收集激活信息
        wrapped_layers = {}
        for name in subset:
            # print(f"get dense feature map of layer {i} name {name}")
            wrapped_layers[name] = WrappedGPT(subset[name])

        # print(f"get dense feature map of layer {i}")

        # 定义前向传播钩子函数，用于收集输入输出数据
        def add_batch(name):
            def tmp(_, inp, out):
                wrapped_layers[name].add_batch(inp[0].data, out.data)
            return tmp

        # 注册前向传播钩子
        handles = []
        for name in wrapped_layers:
            handles.append(subset[name].register_forward_hook(add_batch(name)))
            
        # 通过所有校准样本进行前向传播，收集特征
        for j in range(args.nsamples):
            with torch.no_grad():
                if "OPT" in model.__class__.__name__:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask)[0]
                else:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask, position_ids=position_ids)[0]

        # 移除钩子函数
        for h in handles:
            h.remove()
        
        # 保存当前层的输出特征（克隆到CPU以节省GPU内存）
        feature.append(outs.clone().detach().cpu())

        # 为下一层准备输入：当前层的输出作为下一层的输入
        inps, outs = outs, inps

    

    return feature


def find_layers(module, layers=[nn.Linear], name=""):
    """
    递归查找指定类型的层
    
    关键功能：
    1. 在模型中递归搜索所有线性层（nn.Linear）
    2. 排除包含'lora'的层名，避免操作LoRA适配器层
    3. 返回层名到层对象的映射字典
    
    Args:
        module: PyTorch模块
        layers: 要查找的层类型列表，默认为[nn.Linear]
        name: 模块名称
        
    Returns:
        res: 包含找到的层的字典 {层名: 层对象}
    """
    # 如果当前模块就是目标类型且不是LoRA层，直接返回
    if type(module) in layers and 'lora' not in name:
        return {name: module}
    
    res = {}
    # 递归搜索子模块
    for name1, child in module.named_children():
        res.update(
            find_layers(
                child, layers=layers, name=name + "." + name1 if name != "" else name1
            )
        )
    return res


def check_sparsity(model):
    """
    检查模型的稀疏度
    
    稀疏度计算原理：
    1. 统计模型中所有权重为0的参数数量
    2. 计算稀疏度 = 零权重数量 / 总权重数量
    3. 分层输出每层的稀疏度便于分析
    
    Args:
        model: 要检查的模型
        
    Returns:
        float: 整体稀疏度比例
    """
    use_cache = model.config.use_cache
    model.config.use_cache = False

    layers = model.model.layers
    count = 0        # 总的零权重数量
    total_params = 0 # 总的参数数量
    
    # 逐层检查稀疏度
    for i in range(len(layers)):
        layer = layers[i]
        subset = find_layers(layer)  # 找到当前层的所有线性层

        sub_count = 0   # 当前层的零权重数量  
        sub_params = 0  # 当前层的总参数数量
        
        for name in subset:
            W = subset[name].weight.data
            # 统计权重为0的参数数量
            sub_module_count = (W == 0).sum().item()
            count += (W == 0).sum().item()
            # 统计总参数数量
            sub_module_params = W.numel()
            total_params += W.numel()

            sub_count += (W == 0).sum().item()
            sub_params += W.numel()

        # 输出每层的稀疏度
        print(f"layer {i} sparsity {float(sub_count)/sub_params:.6f}")

    model.config.use_cache = use_cache
    # 返回整体稀疏度
    return float(count) / total_params


def prepare_calibration_input(args, model, dataloader, device):
    """
    准备校准输入数据
    
    核心机制：
    1. 使用Catcher类拦截第一层的输入
    2. 通过数据加载器运行少量样本来收集真实的输入分布
    3. 这些输入将用于后续的剪枝校准过程
    
    Args:
        args: 命令行参数
        model: 模型
        dataloader: 数据加载器
        device: 计算设备
        
    Returns:
        inps: 输入张量
        outs: 输出张量（初始化为零）
        attention_mask: 注意力掩码
        position_ids: 位置编码
    """
    use_cache = model.config.use_cache
    model.config.use_cache = False
    layers = model.model.layers

    if "model.embed_tokens" in model.hf_device_map:
        device = model.hf_device_map["model.embed_tokens"]

    dtype = next(iter(model.parameters())).dtype

    # print("-----debug-----")
    # print(args.nsamples)
    # print("-----debug-----")

    inps = torch.zeros(
        (args.nsamples, model.seqlen, model.config.hidden_size),
        dtype=dtype,
        device=device,
    )
    inps.requires_grad = False
    cache = {"i": 0, "attention_mask": None, "position_ids": None}

    class Catcher(nn.Module):
        def __init__(self, module):
            super().__init__()
            self.module = module

        def forward(self, inp, **kwargs):
            inps[cache["i"]] = inp
            cache["i"] += 1
            cache["attention_mask"] = kwargs["attention_mask"]
            cache["position_ids"] = kwargs["position_ids"]
            raise ValueError

    layers[0] = Catcher(layers[0])
    for batch in dataloader:
        try:
            model(batch[0].to(device))
        except ValueError:
            pass
    layers[0] = layers[0].module

    outs = torch.zeros_like(inps)
    attention_mask = cache["attention_mask"]
    position_ids = cache["position_ids"]
    model.config.use_cache = use_cache

    return inps, outs, attention_mask, position_ids


def prune_wanda(args, model, tokenizer, device=torch.device("cuda:0"), prune_n=0, prune_m=0, 
    dataloader=None, calib_dataloader=None, dense_feature=None, prune_iter=0, iters=10,
):
    """
    Wanda剪枝方法实现
    
    Wanda剪枝核心原理：
    1. 基于权重大小和输入激活的幅度来计算重要性分数
    2. 重要性分数 = |权重| × sqrt(输入激活的二阶统计量)
    3. 保留重要性分数高的权重，将分数低的权重置零
    4. 使用CKA相似性分析来确定每层的剪枝比例
    
    与传统剪枝的区别：
    - 不仅考虑权重大小，还考虑激活模式
    - 使用层间相似性指导剪枝分配
    - 支持渐进式剪枝（多次迭代）
    
    Args:
        args: 命令行参数
        model: 要剪枝的模型
        tokenizer: 分词器
        device: 计算设备
        prune_n, prune_m: N:M结构化稀疏参数
        dataloader: 主数据加载器
        calib_dataloader: 校准数据加载器  
        dense_feature: 密集模型的特征图
        prune_iter: 当前迭代次数
        iters: 总迭代次数
        
    Returns:
        recon_loss: 每层的重构损失列表
    """
    
    # 计算当前迭代的目标稀疏度（渐进式剪枝）
    # 使用立方衰减公式：逐渐增加稀疏度直到最终目标
    iter_sparsity_ratio = args.sparsity_ratio-args.sparsity_ratio*(1-prune_iter/iters)**3

    
    # 根据当前稀疏度动态调整层间稀疏度变化范围
    # delta_ratio控制各层稀疏度与平均稀疏度的允许偏差
    if iter_sparsity_ratio <= 0.5:
        args.delta_ratio = 0.01  # 低稀疏度时允许较小偏差
    elif iter_sparsity_ratio > 0.5 and iter_sparsity_ratio <= 0.6:
        args.delta_ratio = 0.02  # 中等稀疏度时允许中等偏差
    elif iter_sparsity_ratio > 0.6 and iter_sparsity_ratio <= 0.7:
        args.delta_ratio = 0.03  # 高稀疏度时允许较大偏差
    else:
        args.delta_ratio = 0.05

    args_nsamples = args.nsamples
    # 根据模型大小调整校准样本数量（大模型用更少样本以节省内存）
    if '7' in args.model or '8' in args.model:
        args.nsamples=64   # 7B/8B模型使用64个样本
    elif '13' in args.model:
        args.nsamples=32   # 13B模型使用32个样本
    
    model.config.use_cache = False
    
    # 第一阶段：收集当前（稀疏）模型的激活特征用于CKA分析
    with torch.no_grad():
        inps, outs, attention_mask, position_ids = prepare_calibration_input(
            args, model, calib_dataloader, device
        )
    
    layers = model.model.layers

    # 收集各层特征用于相似性分析
    feature = []
    for i in range(len(layers)):
        layer = layers[i]

        subset = find_layers(layer)

        # 处理多GPU设备映射
        if f"model.layers.{i}" in model.hf_device_map:   
            dev = model.hf_device_map[f"model.layers.{i}"]
            inps, outs, attention_mask, position_ids = inps.to(dev), outs.to(dev), attention_mask.to(dev), position_ids.to(dev)

        # 为统计收集创建层包装器
        wrapped_layers = {}
        for name in subset:
            wrapped_layers[name] = WrappedGPT(subset[name])

        print(f"get sparse feature map of layer {i}")

        # 定义批次添加函数，收集激活统计
        def add_batch(name):
            def tmp(_, inp, out):
                wrapped_layers[name].add_batch(inp[0].data, out.data)
            return tmp

        # 注册前向钩子
        handles = []
        for name in wrapped_layers:
            handles.append(subset[name].register_forward_hook(add_batch(name)))
            
        # 前向传播收集特征
        for j in range(args.nsamples):
            with torch.no_grad():
                if "OPT" in model.__class__.__name__:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask)[0]
                else:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask, position_ids=position_ids)[0]

        # 移除钩子
        for h in handles:
            h.remove()
        
        # 保存当前层的输入特征（用于CKA分析）
        feature.append(inps.clone().detach())

        # 为下一层准备输入
        inps, outs = outs, inps
    
    # 将特征重塑为适合CKA计算的格式
    for i in range(len(feature)):
        feature[i] = feature[i].view(args.nsamples, -1)

    # 计算层间特征相似性矩阵（使用CKA指标）
    similar_matrix = torch.zeros((len(feature), len(feature)), device=feature[0].device)

    # 计算所有层对之间的CKA相似性
    for i in range(len(feature)):
        for j in range(len(feature)):
            with torch.no_grad():
                # CKA计算：衡量两个特征表示的线性相关性
                similar_matrix[i][j] = cka.cka(cka.gram_linear(feature[i].float()), cka.gram_linear(feature[j].float()))
        

    def sum_list(a, j):
        """计算除第j个元素外的所有元素之和"""
        b = 0
        for i in range(len(a)):
            if i != j:
                b += a[i]
        return b

    # 基于CKA相似性计算层重要性
    global important, length
    important = []
    temp = []

    # 计算每层与其他所有层的相似性总和
    for i in range(len(feature)):
        temp.append( sum_list(similar_matrix[i], i) )

    # 归一化相似性分数
    b = sum_list(temp, -1)
    temp = [x/b for x in temp]

    # 使用指数函数将相似性转换为重要性权重
    # 相似性越高的层，重要性越低（因为它更容易被其他层替代）
    beta = 1  # Wanda方法使用较小的beta值
    for i in range(len(feature)):
        important.append( torch.exp(-1* beta *temp[i] ) )
    
    length = len(important)

    important = np.array([t.cpu().numpy() for t in important])
    feature.clear()
    del feature
    torch.cuda.empty_cache()

    # Objective function
    def func(x, sign=1.0):
        """ Objective function """
        sum_fuc =[]
        for idx1 in range(length):
            sum_fuc.append(x[idx1]*important[idx1])
        return sum(sum_fuc)


    # Derivative function of objective function
    def func_deriv(x, sign=1.0):
        """ Derivative of objective function """
        global important
        diff = []
        for i in range(len(important)):
            diff.append(sign * (important[i]))
        return np.array(diff)

    # Constraint function
    def constrain_func(x):
        """ constrain function """
        return np.mean(x) - iter_sparsity_ratio

    bnds = []
    for i in range(length):
        bnds.append((iter_sparsity_ratio-args.delta_ratio, iter_sparsity_ratio+args.delta_ratio))

    bnds = tuple(bnds)
    cons = ({'type': 'eq', 'fun': constrain_func},)

    # 使用SLSQP优化器求解最优稀疏度分配
    result = optimize.minimize(func, x0=[1 for i in range(length)], jac=func_deriv, method='SLSQP', bounds=bnds, constraints=cons)
    all_layer_ratio = result.x.tolist()

    # 恢复完整数据集的样本数量用于实际剪枝
    args.nsamples = args_nsamples

    # === 第三阶段：执行实际的权重剪枝 ===
    use_cache = model.config.use_cache
    model.config.use_cache = False
    
    # 重新准备校准数据用于剪枝过程
    with torch.no_grad():
        inps, outs, attention_mask, position_ids = prepare_calibration_input(
            args, model, dataloader, device
        )
    
    layers = model.model.layers
    recon_loss = []  # 存储每层的重构损失
    # 逐层执行Wanda剪枝
    for i in range(len(layers)):
        layer = layers[i]
        subset = find_layers(layer)  # 找到当前层的所有线性层

        # 处理多GPU设备映射
        if (
            f"model.layers.{i}" in model.hf_device_map
        ):  
            dev = model.hf_device_map[f"model.layers.{i}"]
            if inps is not None:
                inps = inps.to(dev)
            if outs is not None:
                outs = outs.to(dev)
            if attention_mask is not None:
                attention_mask = attention_mask.to(dev)
            if position_ids is not None:
                position_ids = position_ids.to(dev)

        # 创建层包装器收集激活统计
        wrapped_layers = {}
        for name in subset:
            wrapped_layers[name] = WrappedGPT(subset[name])

        def add_batch(name):
            def tmp(_, inp, out):
                wrapped_layers[name].add_batch(inp[0].data, out.data)
            return tmp

        # 注册钩子并进行前向传播收集统计
        handles = []
        for name in wrapped_layers:
            handles.append(subset[name].register_forward_hook(add_batch(name)))
        for j in range(args.nsamples):
            with torch.no_grad():
                outs[j] = layer(
                    inps[j].unsqueeze(0),
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                )[0]
        for h in handles:
            h.remove()

        # 对当前层的每个线性子层执行Wanda剪枝
        for name in subset:
            print(f"pruning layer {i} name {name}")
            
            # === Wanda剪枝的核心公式 ===
            # W_metric = |权重| × sqrt(输入激活的二阶统计量)
            # 这个指标综合考虑了权重大小和激活模式
            W_metric = torch.abs(subset[name].weight.data) * torch.sqrt(
                wrapped_layers[name].scaler_row.reshape((1, -1))
            )

            # 初始化剪枝掩码（全False，表示都保留）
            W_mask = (
                torch.zeros_like(W_metric) == 1
            )  
            
            if prune_n != 0:
                # N:M结构化稀疏：在每M个连续元素中剪枝N个
                for ii in range(W_metric.shape[1]):
                    if ii % prune_m == 0:
                        tmp = W_metric[:, ii : (ii + prune_m)].float()
                        # 找到每个M元素块中最小的N个元素进行剪枝
                        W_mask.scatter_(
                            1,
                            ii + torch.topk(tmp, prune_n, dim=1, largest=False)[1],
                            True,
                        )
            else:
                # 非结构化稀疏：直接按重要性分数排序
                sort_res = torch.sort(W_metric, dim=-1, stable=True)

                # 防止负稀疏度
                if all_layer_ratio[i] < 0:
                    all_layer_ratio[i] = 0

                # 计算要剪枝的权重数量并设置掩码
                indices = sort_res[1][:, : int(W_metric.shape[1] * all_layer_ratio[i])]
                W_mask.scatter_(1, indices, True)

            # 执行剪枝：将选中的权重设为0
            subset[name].weight.data[W_mask] = 0

        # 计算剪枝后的前向传播结果
        for j in range(args.nsamples):
            with torch.no_grad():
                outs[j] = layer(
                    inps[j].unsqueeze(0),
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                )[0]

        # 计算重构损失：比较剪枝前后的特征差异
        dense_feature[i] = dense_feature[i].cuda()
        # 使用L2范数归一化来稳定损失计算
        l2_scaler = torch.norm(dense_feature[i].reshape((-1, dense_feature[i].shape[-1])).t(), p=2, dim=1).detach()
        # 确保dense_feature和outs的第一个维度匹配
        if dense_feature[i].shape[0] != outs.shape[0]:
            print(f"维度不匹配: dense_feature[{i}].shape={dense_feature[i].shape}, outs.shape={outs.shape}")
            # 如果样本数量不同，只使用两者中较小的数量
            min_samples = min(dense_feature[i].shape[0], outs.shape[0])
            dense_feat = dense_feature[i][:min_samples]
            outs_subset = outs[:min_samples]
            l2_loss = (((dense_feat - outs_subset) / l2_scaler) ** 2).sum() / outs_subset.shape[-1]
        else:
            l2_loss = (((dense_feature[i] - outs) / l2_scaler) ** 2).sum() / outs.shape[-1]
        recon_loss.append(l2_loss.item())
        dense_feature[i] = dense_feature[i].cpu()

        # 为下一层准备输入
        inps, outs = outs, inps

        for j in range(args.nsamples):
            with torch.no_grad():
                outs[j] = layer(
                    inps[j].unsqueeze(0),
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                )[0]

        dense_feature[i] = dense_feature[i].cuda()
        l2_scaler = torch.norm(dense_feature[i].reshape((-1, dense_feature[i].shape[-1])).t(), p=2, dim=1).detach()
        # 确保dense_feature和outs的第一个维度匹配
        if dense_feature[i].shape[0] != outs.shape[0]:
            print(f"维度不匹配: dense_feature[{i}].shape={dense_feature[i].shape}, outs.shape={outs.shape}")
            # 如果样本数量不同，只使用两者中较小的数量
            min_samples = min(dense_feature[i].shape[0], outs.shape[0])
            dense_feat = dense_feature[i][:min_samples]
            outs_subset = outs[:min_samples]
            l2_loss = (((dense_feat - outs_subset) / l2_scaler) ** 2).sum() / outs_subset.shape[-1]
        else:
            l2_loss = (((dense_feature[i] - outs) / l2_scaler) ** 2).sum() / outs.shape[-1]
        recon_loss.append(l2_loss.item())
        dense_feature[i] = dense_feature[i].cpu()

        inps, outs = outs, inps

    dense_feature = [feature.cpu() for feature in dense_feature]

    model.config.use_cache = use_cache
    torch.cuda.empty_cache()

    ################################################################
    print("*"*30)
    sparsity_ratio = check_sparsity(model)
    print(f"sparsity sanity check {sparsity_ratio:.4f}")
    print("*"*30)
    ################################################################

    gc.collect()

    return recon_loss


def _select_optimal_predictor_v2(layer_sparsity, layer_ratio, layer_importance, sublayer_name, layer_id):
    """
    优化的智能预测器选择算法 V2

    基于多因素分析和实际性能反馈优化的选择策略：
    1. 修正重要性分数的解释和使用
    2. 平衡各预测器的选择概率
    3. 基于实际稀疏度需求进行选择
    4. 考虑子层特性和架构先验知识

    Args:
        layer_sparsity: 子层分配的稀疏度 [0, 1]
        layer_ratio: 整层目标稀疏度 [0, 1]
        layer_importance: 层重要性分数（CKA相似性的指数变换）
        sublayer_name: 子层名称
        layer_id: 层ID

    Returns:
        tuple: (strategy_name, selection_reason)
    """

    # === 1. 特征工程 ===
    sparsity_level = layer_sparsity

    # 重要性分数修正：layer_importance是exp(-beta * similarity)
    # 值越小表示相似性越高，层越不重要；值越大表示层越独特重要
    # 我们需要将其归一化到合理范围
    importance_factor = min(layer_importance / 2.0, 1.0)  # 归一化到[0,1]

    # 稀疏度压力：当前稀疏度相对于目标的压力
    sparsity_pressure = sparsity_level / (layer_ratio + 1e-8)

    # 子层类型分析
    sublayer_name_lower = sublayer_name.lower()

    # === 2. 子层特性评估 ===
    if 'q_proj' in sublayer_name_lower:
        sublayer_type = "attention_query"
        criticality = 0.95  # 最关键
        complexity = 0.8    # 高复杂度
    elif 'k_proj' in sublayer_name_lower:
        sublayer_type = "attention_key"
        criticality = 0.90  # 很关键
        complexity = 0.8    # 高复杂度
    elif 'v_proj' in sublayer_name_lower:
        sublayer_type = "attention_value"
        criticality = 0.85  # 关键
        complexity = 0.7    # 中高复杂度
    elif 'o_proj' in sublayer_name_lower:
        sublayer_type = "attention_output"
        criticality = 0.75  # 中等关键
        complexity = 0.6    # 中等复杂度
    elif 'gate_proj' in sublayer_name_lower:
        sublayer_type = "ffn_gate"
        criticality = 0.80  # 关键
        complexity = 0.7    # 中高复杂度
    elif 'up_proj' in sublayer_name_lower:
        sublayer_type = "ffn_up"
        criticality = 0.65  # 中等关键
        complexity = 0.5    # 中等复杂度
    elif 'down_proj' in sublayer_name_lower:
        sublayer_type = "ffn_down"
        criticality = 0.55  # 较低关键
        complexity = 0.4    # 较低复杂度
    else:
        sublayer_type = "unknown"
        criticality = 0.7
        complexity = 0.6

    # === 3. 深度因子 ===
    if layer_id < 4:
        depth_sensitivity = 0.9   # 早期层敏感
    elif layer_id < 12:
        depth_sensitivity = 0.7   # 中前期层
    elif layer_id > 20:
        depth_sensitivity = 0.3   # 后期层不敏感
    else:
        depth_sensitivity = 0.5   # 中间层

    # === 4. 预测器适应性评分 ===

    # WANDA适应性：稳定性和通用性
    wanda_stability = 1.0 - abs(sparsity_level - 0.4)  # 在40%稀疏度附近最稳定
    wanda_criticality = criticality  # 关键层更适合WANDA
    wanda_universality = 0.8  # WANDA的通用性
    wanda_score = (0.4 * wanda_stability +
                   0.4 * wanda_criticality +
                   0.2 * wanda_universality)

    # Dolphin适应性：高稀疏度和复杂场景
    dolphin_sparsity = min(sparsity_level * 1.5, 1.0)  # 高稀疏度优势
    dolphin_complexity = complexity  # 复杂层更适合Dolphin
    dolphin_adaptivity = sparsity_pressure * 0.5  # 稀疏度压力大时更适合
    dolphin_score = (0.5 * dolphin_sparsity +
                     0.3 * dolphin_complexity +
                     0.2 * dolphin_adaptivity)

    # SNIP适应性：梯度敏感和特定条件
    snip_gradient_sensitivity = importance_factor * (1.0 - depth_sensitivity)  # 重要且非早期层
    snip_sparsity_sweet_spot = 1.0 - abs(sparsity_level - 0.35)  # 在35%稀疏度附近较优
    snip_ffn_preference = 0.3 if 'proj' in sublayer_name_lower and 'gate' not in sublayer_name_lower else 0.0
    snip_score = (0.5 * snip_gradient_sensitivity +
                  0.3 * snip_sparsity_sweet_spot +
                  0.2 * snip_ffn_preference)

    # === 5. 平衡性调整 ===
    # 避免某个预测器过度占优，增加选择的多样性
    scores = [wanda_score, dolphin_score, snip_score]
    max_score = max(scores)

    # 如果最高分过于突出，进行平衡调整
    if max_score > 0.8:
        balance_factor = 0.9
        wanda_score = wanda_score * balance_factor + 0.1
        dolphin_score = dolphin_score * balance_factor + 0.1
        snip_score = snip_score * balance_factor + 0.1

    # === 6. 最终选择 ===
    scores_dict = {
        'wanda': wanda_score,
        'dolphin': dolphin_score,
        'snip': snip_score
    }

    best_strategy = max(scores_dict.keys(), key=lambda k: scores_dict[k])

    # === 7. 生成选择原因 ===
    reason_parts = [
        f"稀疏度={sparsity_level:.1%}",
        f"重要性={importance_factor:.2f}",
        f"类型={sublayer_type}",
        f"得分[W:{wanda_score:.2f},D:{dolphin_score:.2f},S:{snip_score:.2f}]"
    ]

    # 添加主要选择原因
    if best_strategy == 'wanda':
        if wanda_criticality > 0.8:
            reason_parts.append("关键层")
        if wanda_stability > 0.7:
            reason_parts.append("稳定性")
    elif best_strategy == 'dolphin':
        if dolphin_sparsity > 0.7:
            reason_parts.append("高稀疏度")
        if complexity > 0.6:
            reason_parts.append("复杂场景")
    elif best_strategy == 'snip':
        if snip_gradient_sensitivity > 0.5:
            reason_parts.append("梯度敏感")
        if snip_ffn_preference > 0:
            reason_parts.append("FFN优势")

    selection_reason = ", ".join(reason_parts)

    return best_strategy, selection_reason


def prune_giraffe(args, model, tokenizer, device=torch.device("cuda:0"), prune_n=0, prune_m=0,
                    dataloader=None, calib_dataloader = None, dense_feature=None, prune_iter=0, iters=10):

    # 计算当前迭代的目标稀疏度（渐进式剪枝）
    iter_sparsity_ratio = args.sparsity_ratio-args.sparsity_ratio*(1-prune_iter/iters)**3

    ###### 逐层稀疏度设置 ######
    if iter_sparsity_ratio <= 0.5:
        args.delta_ratio = 0.01
    elif iter_sparsity_ratio > 0.5 and iter_sparsity_ratio <= 0.6:
        args.delta_ratio = 0.02
    elif iter_sparsity_ratio > 0.6 and iter_sparsity_ratio <= 0.7:
        args.delta_ratio = 0.03
    else:
        args.delta_ratio = 0.05

    args_nsamples = args.nsamples
    # 根据模型大小调整校准样本数量（大模型用更少样本以节省内存）
    if '7' in args.model or '8' in args.model:
        args.nsamples=64   # 7B/8B模型使用64个样本
    elif '13' in args.model:
        args.nsamples=32   # 13B模型使用32个样本
    
    model.config.use_cache = False
    with torch.no_grad():
        inps, outs, attention_mask, position_ids = prepare_calibration_input(
            args, model, calib_dataloader, device
        )
    layers = model.model.layers

    feature = []
    for i in range(len(layers)):
        layer = layers[i]

        subset = find_layers(layer)

        # 处理多GPU情况下的设备映射 - 适用于llama-30B和llama-65B等大模型
        if f"model.layers.{i}" in model.hf_device_map:   
            dev = model.hf_device_map[f"model.layers.{i}"]
            inps, outs, attention_mask, position_ids = inps.to(dev), outs.to(dev), attention_mask.to(dev), position_ids.to(dev)

        wrapped_layers = {}
        for name in subset:
            wrapped_layers[name] = WrappedGPT(subset[name])

        print(f"获取第{i}层的稀疏特征图")

        def add_batch(name):
            def tmp(_, inp, out):
                wrapped_layers[name].add_batch(inp[0].data, out.data)
            return tmp

        handles = []
        for name in wrapped_layers:
            handles.append(subset[name].register_forward_hook(add_batch(name)))
        for j in range(args.nsamples):
            with torch.no_grad():
                if "OPT" in model.__class__.__name__:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask)[0]
                else:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask, position_ids=position_ids)[0]

        for h in handles:
            h.remove()
        
        feature.append(inps.clone().detach())

        inps, outs = outs, inps
    
    
    for i in range(len(feature)):
        feature[i] = feature[i].view(args.nsamples, -1).to(device)

    similar_matrix = torch.zeros((len(feature), len(feature)), device=feature[0].device)


    for i in range(len(feature)):
        for j in range(len(feature)):
            with torch.no_grad():
                similar_matrix[i][j] = cka.cka(cka.gram_linear(feature[i].float()), cka.gram_linear(feature[j].float()))
        

        def sum_list(a, j):
            """计算除第j个元素外的所有元素之和"""
            b = 0
            for i in range(len(a)):
                if i != j:
                    b += a[i]
            return b

        global important, length
        important = []
        temp = []

        # 计算每层与其他所有层的相似性总和
        for i in range(len(feature)):
            temp.append( sum_list(similar_matrix[i], i) )

        # 归一化相似性分数
        b = sum_list(temp, -1)
        temp = [x/b for x in temp]

        # 使用指数函数将相似性转换为重要性权重
        beta = 100
        for i in range(len(feature)):
            important.append( torch.exp(-1* beta *temp[i] ) )
    
    length = len(important)

    important = np.array([t.cpu().numpy() for t in important])
    feature.clear()
    del feature
    torch.cuda.empty_cache()

    # 目标函数
    def func(x, sign=1.0):
        """ 目标函数 """
        sum_fuc =[]
        for idx1 in range(length):
            sum_fuc.append(x[idx1]*important[idx1])
        return sum(sum_fuc)


    # 目标函数的导数
    def func_deriv(x, sign=1.0):
        """ 目标函数的导数 """
        global important
        diff = []
        for i in range(len(important)):
            diff.append(sign * (important[i]))
        return np.array(diff)

    # 约束函数
    def constrain_func(x):
        """ 约束函数 """
        return np.mean(x) - iter_sparsity_ratio

    bnds = []
    for i in range(length):
        bnds.append((iter_sparsity_ratio-args.delta_ratio, iter_sparsity_ratio+args.delta_ratio))

    bnds = tuple(bnds)
    cons = ({'type': 'eq', 'fun': constrain_func},)

    # 使用SLSQP优化器求解最优稀疏度分配
    result = optimize.minimize(func, x0=[1 for i in range(length)], jac=func_deriv, method='SLSQP', bounds=bnds, constraints=cons)
    all_layer_ratio = result.x.tolist()

    args.nsamples = args_nsamples
    ###### 逐层稀疏度设置 ######

    use_cache = model.config.use_cache
    model.config.use_cache = False

    with torch.no_grad():
        inps, outs, attention_mask, position_ids = prepare_calibration_input(
            args, model, dataloader, device
        )

    layers = model.model.layers
    recon_loss = []

    # 逐层执行剪枝
    for i in range(len(layers)):
        layer = layers[i]
        if f"model.layers.{i}" in model.hf_device_map:
            dev = model.hf_device_map[f"model.layers.{i}"]
            print(f"第{i}层 设备 {dev}")
            if inps is not None:
                inps = inps.to(dev)
            if outs is not None:
                outs = outs.to(dev)
            if attention_mask is not None:
                attention_mask = attention_mask.to(dev)
            if position_ids is not None:
                position_ids = position_ids.to(dev)

        subset = find_layers(layer)

        # 为每个线性层创建剪枝器
        giraffe = {}
        for name in subset:
            giraffe[name] = Giraffe(subset[name], layer_id=i, layer_name=name)

        def add_batch(name):
            def tmp(_, inp, out):
                giraffe[name].add_batch(inp[0].data, out.data)

            return tmp

        handles = []
        for name in giraffe:
            handles.append(subset[name].register_forward_hook(add_batch(name)))

        for j in range(args.nsamples):
            with torch.no_grad():
                outs[j] = layer(
                    inps[j].unsqueeze(0),
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                )[0]
        for h in handles:
            h.remove()

        # 对当前层的每个线性子层执行剪枝
        # 使用简化的动态稀疏度分配，减少复杂性
        layer_sparsities = Giraffe.dynamic_sparsity(giraffe, all_layer_ratio[i], i)

        # 记录预测器选择统计
        predictor_stats = {"wanda": 0, "dolphin": 0, "snip": 0}

        for name in giraffe:
            print(f"剪枝第{i}层 {name}")
            # 获取为该子层分配的特定稀疏度
            layer_sparsity = layer_sparsities.get(name, all_layer_ratio[i])
            if layer_sparsity < 0:
                layer_sparsity = 0

            # === 智能预测器策略选择 V2 ===
            # 使用优化的多因素分析算法选择最优预测器
            strategy, selection_reason = _select_optimal_predictor_v2(
                layer_sparsity=layer_sparsity,
                layer_ratio=all_layer_ratio[i],
                layer_importance=important[i],
                sublayer_name=name,
                layer_id=i
            )

            print(f"  使用策略: {strategy} (稀疏度: {layer_sparsity:.2%}) - {selection_reason}")
            giraffe[name].set_predictor_strategy(strategy)

            # 统计预测器使用情况
            predictor_stats[strategy] += 1

            giraffe[name].prune(layer_sparsity)
            giraffe[name].free()
        
        # 计算并输出层总体剪枝统计
        layer_total_params = sum(giraffe[name].layer.weight.numel() for name in giraffe.keys())
        layer_total_pruned = sum((giraffe[name].layer.weight.data == 0).sum().item() for name in giraffe.keys())
        layer_actual_sparsity = layer_total_pruned / layer_total_params if layer_total_params > 0 else 0
        
        print(f"第{i}层剪枝完成汇总:")
        print(f"  层总参数: {layer_total_params:,}")
        print(f"  层总剪枝: {layer_total_pruned:,}")
        print(f"  层实际稀疏度: {layer_actual_sparsity:.2%} (目标: {all_layer_ratio[i]:.2%}, 偏差: {abs(layer_actual_sparsity - all_layer_ratio[i]):.2%})")

        # 显示预测器选择统计
        total_sublayers = sum(predictor_stats.values())
        if total_sublayers > 0:
            print(f"  预测器选择统计:")
            for predictor, count in predictor_stats.items():
                percentage = (count / total_sublayers) * 100
                print(f"    {predictor}: {count}/{total_sublayers} ({percentage:.1f}%)")
        print()

        for j in range(args.nsamples):
            with torch.no_grad():
                outs[j] = layer(
                    inps[j].unsqueeze(0),
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                )[0]

        layers[i] = layer
        torch.cuda.empty_cache()

        # 计算重构损失：比较剪枝前后的特征差异
        dense_feature[i] = dense_feature[i].cuda()
        # 使用L2范数归一化来稳定损失计算
        l2_scaler = torch.norm(dense_feature[i].reshape((-1, dense_feature[i].shape[-1])).t(), p=2, dim=1).detach()
        # 确保dense_feature和outs的第一个维度匹配
        if dense_feature[i].shape[0] != outs.shape[0]:
            print(f"维度不匹配: dense_feature[{i}].shape={dense_feature[i].shape}, outs.shape={outs.shape}")
            # 如果样本数量不同，只使用两者中较小的数量
            min_samples = min(dense_feature[i].shape[0], outs.shape[0])
            dense_feat = dense_feature[i][:min_samples]
            outs_subset = outs[:min_samples]
            l2_loss = (((dense_feat - outs_subset) / l2_scaler) ** 2).sum() / outs_subset.shape[-1]
        else:
            l2_loss = (((dense_feature[i] - outs) / l2_scaler) ** 2).sum() / outs.shape[-1]
        recon_loss.append(l2_loss.item())
        dense_feature[i] = dense_feature[i].cpu()

        # 为下一层准备输入
        inps, outs = outs, inps

    # 将特征图移到CPU以节省GPU内存
    dense_feature = [feature.cpu() for feature in dense_feature]

    model.config.use_cache = use_cache
    torch.cuda.empty_cache()

    ################################################################
    print("*"*30)
    sparsity_ratio = check_sparsity(model)
    print(f"稀疏度检查 {sparsity_ratio:.4f}")
    print("*"*30)
    ################################################################

    gc.collect()

    return recon_loss


@torch.no_grad()
def prune_sparsegpt(args, model, tokenizer, device=torch.device("cuda:0"), prune_n=0, prune_m=0, 
                    dataloader=None, calib_dataloader = None, dense_feature=None, prune_iter=0, iters=10):
    """
    SparseGPT剪枝方法实现
    
    SparseGPT剪枝核心原理：
    1. 基于二阶信息的逐层剪枝算法
    2. 使用Hessian矩阵信息来选择最重要的权重
    3. 通过求解最优化问题来最小化剪枝后的重构误差
    4. 支持结构化和非结构化稀疏模式
    
    Args:
        args: 命令行参数
        model: 要剪枝的模型
        tokenizer: 分词器
        device: 计算设备
        prune_n, prune_m: N:M结构化稀疏参数
        dataloader: 主数据加载器
        calib_dataloader: 校准数据加载器
        dense_feature: 密集模型的特征图
        prune_iter: 当前迭代次数
        iters: 总迭代次数
        
    Returns:
        recon_loss: 每层的重构损失列表
    """

    # 计算当前迭代的目标稀疏度（渐进式剪枝）
    iter_sparsity_ratio = args.sparsity_ratio-args.sparsity_ratio*(1-prune_iter/iters)**3

    ###### 逐层稀疏度设置 ######
    if iter_sparsity_ratio <= 0.5:
        args.delta_ratio = 0.01
    elif iter_sparsity_ratio > 0.5 and iter_sparsity_ratio <= 0.6:
        args.delta_ratio = 0.02
    elif iter_sparsity_ratio > 0.6 and iter_sparsity_ratio <= 0.7:
        args.delta_ratio = 0.03
    else:
        args.delta_ratio = 0.05

    args_nsamples = args.nsamples
    # 根据模型大小调整校准样本数量（大模型用更少样本以节省内存）
    if '7' in args.model or '8' in args.model:
        args.nsamples=64   # 7B/8B模型使用64个样本
    elif '13' in args.model:
        args.nsamples=32   # 13B模型使用32个样本

    
    model.config.use_cache = False
    with torch.no_grad():
        inps, outs, attention_mask, position_ids = prepare_calibration_input(
            args, model, calib_dataloader, device
        )
    layers = model.model.layers

    feature = []
    for i in range(len(layers)):
        layer = layers[i]

        subset = find_layers(layer)

        # 处理多GPU情况下的设备映射 - 适用于llama-30B和llama-65B等大模型
        if f"model.layers.{i}" in model.hf_device_map:   
            dev = model.hf_device_map[f"model.layers.{i}"]
            inps, outs, attention_mask, position_ids = inps.to(dev), outs.to(dev), attention_mask.to(dev), position_ids.to(dev)

        wrapped_layers = {}
        for name in subset:
            wrapped_layers[name] = WrappedGPT(subset[name])

        print(f"获取第{i}层的稀疏特征图")

        def add_batch(name):
            def tmp(_, inp, out):
                wrapped_layers[name].add_batch(inp[0].data, out.data)
            return tmp

        handles = []
        for name in wrapped_layers:
            handles.append(subset[name].register_forward_hook(add_batch(name)))
        for j in range(args.nsamples):
            with torch.no_grad():
                if "OPT" in model.__class__.__name__:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask)[0]
                else:
                    outs[j] = layer(inps[j].unsqueeze(0), attention_mask=attention_mask, position_ids=position_ids)[0]

        for h in handles:
            h.remove()
        
        feature.append(inps.clone().detach())

        inps, outs = outs, inps
    
    
    for i in range(len(feature)):
        feature[i] = feature[i].view(args.nsamples, -1).to(device)

    similar_matrix = torch.zeros((len(feature), len(feature)), device=feature[0].device)


    for i in range(len(feature)):
        for j in range(len(feature)):
            with torch.no_grad():
                similar_matrix[i][j] = cka.cka(cka.gram_linear(feature[i].float()), cka.gram_linear(feature[j].float()))
        

        def sum_list(a, j):
            """计算除第j个元素外的所有元素之和"""
            b = 0
            for i in range(len(a)):
                if i != j:
                    b += a[i]
            return b

        global important, length
        important = []
        temp = []

        # 计算每层与其他所有层的相似性总和
        for i in range(len(feature)):
            temp.append( sum_list(similar_matrix[i], i) )

        # 归一化相似性分数
        b = sum_list(temp, -1)
        temp = [x/b for x in temp]

        # 使用指数函数将相似性转换为重要性权重
        # SparseGPT使用较大的beta值来强化重要性差异
        beta = 100
        for i in range(len(feature)):
            important.append( torch.exp(-1* beta *temp[i] ) )
    
    length = len(important)

    important = np.array([t.cpu().numpy() for t in important])
    feature.clear()
    del feature
    torch.cuda.empty_cache()

    # 目标函数
    def func(x, sign=1.0):
        """ 目标函数 """
        sum_fuc =[]
        for idx1 in range(length):
            sum_fuc.append(x[idx1]*important[idx1])
        return sum(sum_fuc)


    # 目标函数的导数
    def func_deriv(x, sign=1.0):
        """ 目标函数的导数 """
        global important
        diff = []
        for i in range(len(important)):
            diff.append(sign * (important[i]))
        return np.array(diff)

    # 约束函数
    def constrain_func(x):
        """ 约束函数 """
        return np.mean(x) - iter_sparsity_ratio

    bnds = []
    for i in range(length):
        bnds.append((iter_sparsity_ratio-args.delta_ratio, iter_sparsity_ratio+args.delta_ratio))

    bnds = tuple(bnds)
    cons = ({'type': 'eq', 'fun': constrain_func},)

    # 使用SLSQP优化器求解最优稀疏度分配
    result = optimize.minimize(func, x0=[1 for i in range(length)], jac=func_deriv, method='SLSQP', bounds=bnds, constraints=cons)
    all_layer_ratio = result.x.tolist()

    args.nsamples = args_nsamples
    ###### 逐层稀疏度设置 ######

    use_cache = model.config.use_cache
    model.config.use_cache = False

    with torch.no_grad():
        inps, outs, attention_mask, position_ids = prepare_calibration_input(
            args, model, dataloader, device
        )

    layers = model.model.layers
    recon_loss = []

    # 逐层执行SparseGPT剪枝
    for i in range(len(layers)):
        layer = layers[i]
        if f"model.layers.{i}" in model.hf_device_map:
            dev = model.hf_device_map[f"model.layers.{i}"]
            print(f"第{i}层 设备 {dev}")
            if inps is not None:
                inps = inps.to(dev)
            if outs is not None:
                outs = outs.to(dev)
            if attention_mask is not None:
                attention_mask = attention_mask.to(dev)
            if position_ids is not None:
                position_ids = position_ids.to(dev)

        subset = find_layers(layer)

        # 为每个线性层创建SparseGPT剪枝器
        gpts = {}
        for name in subset:
            gpts[name] = SparseGPT(subset[name])

        def add_batch(name):
            def tmp(_, inp, out):
                gpts[name].add_batch(inp[0].data, out.data)

            return tmp

        handles = []
        for name in gpts:
            handles.append(subset[name].register_forward_hook(add_batch(name)))

        for j in range(args.nsamples):
            with torch.no_grad():
                outs[j] = layer(
                    inps[j].unsqueeze(0),
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                )[0]
        for h in handles:
            h.remove()

        # 对当前层的每个线性子层执行SparseGPT剪枝
        for name in gpts:
            print(f"剪枝第{i}层 {name}")

            if all_layer_ratio[i] < 0:
                all_layer_ratio[i] = 0

            # 执行SparseGPT快速剪枝算法
            gpts[name].fasterprune(
                all_layer_ratio[i],
                prune_n=prune_n,
                prune_m=prune_m,
                percdamp=0.01,
                blocksize=128,
            )

            gpts[name].free()

        for j in range(args.nsamples):
            with torch.no_grad():
                outs[j] = layer(
                    inps[j].unsqueeze(0),
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                )[0]

        layers[i] = layer
        torch.cuda.empty_cache()

        # 计算重构损失：比较剪枝前后的特征差异
        dense_feature[i] = dense_feature[i].cuda()
        # 使用L2范数归一化来稳定损失计算
        l2_scaler = torch.norm(dense_feature[i].reshape((-1, dense_feature[i].shape[-1])).t(), p=2, dim=1).detach()
        # 确保dense_feature和outs的第一个维度匹配
        if dense_feature[i].shape[0] != outs.shape[0]:
            print(f"维度不匹配: dense_feature[{i}].shape={dense_feature[i].shape}, outs.shape={outs.shape}")
            # 如果样本数量不同，只使用两者中较小的数量
            min_samples = min(dense_feature[i].shape[0], outs.shape[0])
            dense_feat = dense_feature[i][:min_samples]
            outs_subset = outs[:min_samples]
            l2_loss = (((dense_feat - outs_subset) / l2_scaler) ** 2).sum() / outs_subset.shape[-1]
        else:
            l2_loss = (((dense_feature[i] - outs) / l2_scaler) ** 2).sum() / outs.shape[-1]
        recon_loss.append(l2_loss.item())
        dense_feature[i] = dense_feature[i].cpu()

        # 为下一层准备输入
        inps, outs = outs, inps

    # 将特征图移到CPU以节省GPU内存
    dense_feature = [feature.cpu() for feature in dense_feature]

    model.config.use_cache = use_cache
    torch.cuda.empty_cache()

    ################################################################
    print("*"*30)
    sparsity_ratio = check_sparsity(model)
    print(f"稀疏度检查 {sparsity_ratio:.4f}")
    print("*"*30)
    ################################################################

    gc.collect()

    return recon_loss


