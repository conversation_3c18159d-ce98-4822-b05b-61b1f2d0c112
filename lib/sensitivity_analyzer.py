"""
敏感性分析和动态稀疏度分配模块

负责：
1. 计算层的敏感性分数
2. 基于敏感性的动态稀疏度分配
3. 层级重要性分析
4. 架构感知的稀疏度策略
"""

import math
import torch
import transformers


class SensitivityAnalyzer:
    """敏感性分析器"""
    
    @staticmethod
    def calculate_layer_sensitivity(layer, activation_stats, layer_id, layer_name):
        """
        简化的敏感性计算函数 - 基于WANDA的核心思想

        核心改进：
        1. 简化为基于权重幅度和激活强度的核心指标
        2. 移除复杂的统计计算，提高数值稳定性
        3. 保留必要的架构感知，但大幅简化
        4. 与WANDA的重要性评估保持一致

        Args:
            layer: 神经网络层
            activation_stats: 激活统计信息
            layer_id: 层ID
            layer_name: 层名称

        Returns:
            float: 敏感性分数，越高表示越重要，应分配更低的稀疏度
        """
        if not activation_stats or len(activation_stats.get('l2_norm', [])) == 0:
            return 1.0  # 默认敏感性

        # === 1. 权重重要性（基于WANDA思想）===
        W = layer.weight.data.clone()
        if isinstance(layer, transformers.Conv1D):
            W = W.t()
        W = W.float()

        # 权重的整体幅度 - 类似WANDA中的权重分量
        weight_magnitude = torch.mean(torch.abs(W)).item()

        # === 2. 激活重要性（基于WANDA思想）===
        l2_norm_mean = torch.mean(activation_stats['l2_norm']).item()
        variance_mean = torch.mean(activation_stats['var']).item()

        # 激活强度：结合L2范数和方差，类似WANDA的激活分量
        activation_strength = math.sqrt(l2_norm_mean * (1 + variance_mean))

        # === 3. 基础敏感性（类似WANDA的重要性公式）===
        # 敏感性 = 权重幅度 × 激活强度
        base_sensitivity = weight_magnitude * activation_strength

        # === 4. 简化的架构调整 ===
        arch_weight = SensitivityAnalyzer._get_simplified_architecture_weight(layer_name)

        # === 5. 简化的深度调整 ===
        depth_weight = SensitivityAnalyzer._get_simplified_depth_weight(layer_id)

        # === 6. 最终敏感性分数 ===
        final_sensitivity = base_sensitivity * arch_weight * depth_weight

        return final_sensitivity

    @staticmethod
    def _get_simplified_architecture_weight(layer_name):
        """获取简化的基于层架构的权重系数"""
        layer_name = layer_name.lower()

        # 大幅简化的层重要性权重，更接近WANDA的简洁性
        if 'q_proj' in layer_name or 'k_proj' in layer_name:
            # 注意力核心层：稍微保守
            return 1.1
        elif 'gate_proj' in layer_name:
            # 门控层：稍微保守
            return 1.05
        elif 'down_proj' in layer_name:
            # 下投影：可以更激进
            return 0.95
        else:
            # 其他层：标准处理
            return 1.0

    @staticmethod
    def _get_simplified_depth_weight(layer_id):
        """获取简化的基于层深度的权重系数"""
        # 简化的深度调整，减少过度工程化
        if layer_id < 4:  # 前4层
            return 1.05
        elif layer_id > 20:  # 后层
            return 0.98
        else:
            return 1.0

    @staticmethod
    def _get_architecture_weight(layer_name):
        """获取基于层架构的权重系数（保留原版本以兼容）"""
        return SensitivityAnalyzer._get_simplified_architecture_weight(layer_name)

    @staticmethod
    def _get_depth_weight(layer_id):
        """获取基于层深度的权重系数（保留原版本以兼容）"""
        return SensitivityAnalyzer._get_simplified_depth_weight(layer_id)


class DynamicSparsityAllocator:
    """动态稀疏度分配器"""
    
    @staticmethod
    def allocate_sparsity(giraffe_layers, target_sparsity, layer_id=0):
        """
        优化的动态稀疏度分配算法 V4 - 保守但精确的稀疏度分配

        核心改进：
        1. 保守的稀疏度分配范围，确保稳定性
        2. 基于敏感性的智能动态调配
        3. 精确的迭代调整机制
        4. 子层间适度的差异化处理

        Args:
            giraffe_layers: {layer_name: Giraffe对象} 的字典
            target_sparsity: 目标平均稀疏度
            layer_id: 层ID

        Returns:
            dict: {layer_name: 分配的稀疏度}
        """
        if not giraffe_layers:
            return {}

        # 计算每个子层的敏感性分数和特性
        sensitivities = {}
        layer_params = {}
        layer_types = {}
        total_params = 0

        print(f"第{layer_id}层开始优化稀疏度分配...")

        for name, giraffe in giraffe_layers.items():
            # 获取激活统计信息
            activation_stats = giraffe.stats_collector.get_stats()

            # 计算敏感性
            sensitivity = SensitivityAnalyzer.calculate_layer_sensitivity(
                giraffe.layer, activation_stats, giraffe.layer_id, giraffe.layer_name
            )
            sensitivities[name] = sensitivity

            # 计算参数数量
            params = giraffe.layer.weight.numel()
            layer_params[name] = params
            total_params += params

            # 确定子层类型
            name_lower = name.lower()
            if 'q_proj' in name_lower:
                layer_types[name] = 'attention_core'
            elif 'k_proj' in name_lower:
                layer_types[name] = 'attention_core'
            elif 'v_proj' in name_lower:
                layer_types[name] = 'attention_value'
            elif 'o_proj' in name_lower:
                layer_types[name] = 'attention_output'
            elif 'gate_proj' in name_lower:
                layer_types[name] = 'ffn_gate'
            elif 'up_proj' in name_lower:
                layer_types[name] = 'ffn_up'
            elif 'down_proj' in name_lower:
                layer_types[name] = 'ffn_down'
            else:
                layer_types[name] = 'unknown'

            print(f"  {name} 敏感性分数: {sensitivity:.4f} (类型: {layer_types[name]})")

        # 检查敏感性分数的分布
        sensitivity_values = list(sensitivities.values())
        min_sens = min(sensitivity_values)
        max_sens = max(sensitivity_values)

        if max_sens - min_sens < 1e-6:  # 所有敏感性相同
            print(f"  敏感性分数相近，使用均匀分配")
            return {name: target_sparsity for name in giraffe_layers.keys()}

        # === 保守但有效的分配策略 ===
        # 1. 适度的稀疏度分配范围
        if target_sparsity <= 0.3:
            max_deviation = 0.08  # 低稀疏度：8%偏差
        elif target_sparsity <= 0.5:
            max_deviation = 0.10  # 中等稀疏度：10%偏差
        else:
            max_deviation = 0.12  # 高稀疏度：12%偏差

        min_sparsity = max(0.05, target_sparsity - max_deviation)
        max_sparsity = min(0.75, target_sparsity + max_deviation)

        print(f"  稀疏度分配范围: [{min_sparsity:.2%}, {max_sparsity:.2%}]")

        # 2. 基于敏感性和类型的初始分配
        sparsity_assignments = {}

        for name, sens in sensitivities.items():
            # 归一化敏感性到[0,1]
            normalized_sens = (sens - min_sens) / (max_sens - min_sens)

            # 基础稀疏度因子：敏感性高的层 -> 稀疏度低
            base_factor = 1.0 - 0.3 * normalized_sens  # 范围[0.7, 1.0]，适度差异

            # 类型调整因子 - 更保守的设置
            layer_type = layer_types[name]
            if layer_type == 'attention_core':
                type_factor = 0.90  # 注意力核心：保守
            elif layer_type == 'attention_value':
                type_factor = 0.95  # 注意力值：稍保守
            elif layer_type == 'attention_output':
                type_factor = 0.98  # 注意力输出：轻微保守
            elif layer_type == 'ffn_gate':
                type_factor = 0.92  # FFN门控：保守
            elif layer_type == 'ffn_up':
                type_factor = 1.00  # FFN上投影：标准
            elif layer_type == 'ffn_down':
                type_factor = 1.08  # FFN下投影：稍激进
            else:
                type_factor = 1.0

            # 组合因子
            combined_factor = base_factor * type_factor

            # 计算该层的稀疏度
            layer_sparsity = target_sparsity * combined_factor

            # 约束到允许范围
            layer_sparsity = max(min_sparsity, min(max_sparsity, layer_sparsity))

            sparsity_assignments[name] = layer_sparsity

        # 3. 精确的迭代调整以达到目标稀疏度
        max_iterations = 10  # 适度的迭代次数
        tolerance = 0.005    # 0.5%的容忍度，平衡精确性和稳定性

        for iteration in range(max_iterations):
            current_avg = sum(sparsity_assignments[name] * layer_params[name]
                             for name in sparsity_assignments.keys()) / total_params

            deviation = target_sparsity - current_avg

            if abs(deviation) < tolerance:  # 偏差小于0.5%，停止调整
                break

            if iteration == 0:
                print(f"  调整稀疏度分配，初始偏差: {deviation:.3%}")

            # 简化的调整策略：基于敏感性和参数量
            adjustment_weights = {}
            total_adjustment_weight = 0

            for name in sparsity_assignments.keys():
                sens = sensitivities[name]
                normalized_sens = (sens - min_sens) / (max_sens - min_sens)
                current_sparsity = sparsity_assignments[name]
                params = layer_params[name]

                # 敏感性权重：敏感性低的层承担更多调整
                sens_weight = 1.0 - normalized_sens

                # 参数量权重：参数多的层有更大调整空间
                param_weight = params / total_params

                # 稀疏度空间权重：确保不超出边界
                if deviation > 0:  # 需要增加稀疏度
                    space_weight = max(0, (max_sparsity - current_sparsity) / max_deviation) if max_deviation > 0 else 0
                else:  # 需要减少稀疏度
                    space_weight = max(0, (current_sparsity - min_sparsity) / max_deviation) if max_deviation > 0 else 0

                # 综合权重
                adjustment_weight = sens_weight * param_weight * space_weight
                adjustment_weights[name] = adjustment_weight
                total_adjustment_weight += adjustment_weight

            # 归一化调整权重
            if total_adjustment_weight > 0:
                for name in adjustment_weights:
                    adjustment_weights[name] /= total_adjustment_weight

            # 应用调整
            for name in sparsity_assignments.keys():
                if adjustment_weights[name] > 0:
                    # 保守的调整幅度
                    adjustment_factor = 0.5  # 固定的保守调整因子
                    adjustment = deviation * adjustment_weights[name] * adjustment_factor

                    adjusted_sparsity = sparsity_assignments[name] + adjustment

                    # 约束到允许范围
                    sparsity_assignments[name] = max(min_sparsity, min(max_sparsity, adjusted_sparsity))

        # 4. 最终结果计算和输出
        final_avg = sum(sparsity_assignments[name] * layer_params[name]
                       for name in sparsity_assignments.keys()) / total_params

        print(f"  稀疏度分配结果:")
        for name, sparsity in sparsity_assignments.items():
            params = layer_params[name]
            pruned = int(params * sparsity)
            type_info = f"({layer_types[name]})"
            print(f"    {name}: {sparsity:.2%} {type_info} (预估剪枝 {pruned:,}/{params:,})")

        print(f"  最终平均稀疏度: {final_avg:.3%} (目标: {target_sparsity:.3%}, 偏差: {abs(final_avg - target_sparsity):.3%})")

        return sparsity_assignments
